import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { ArrowLeft, Save, X } from "lucide-react"

interface ProductFormData {
    name: string
    category: string
    description: string
    price: string
    costPrice: string
    stock: string
    minStock: string
    unit: string
    barcode: string
    supplier: string
}

const categories = [
    "Grains",
    "Spices",
    "Dairy",
    "Instant Food",
    "Snacks",
    "Beverages",
    "Personal Care",
    "Household",
    "Fruits & Vegetables",
    "Frozen Foods"
]

const units = [
    "kg",
    "gm",
    "ltr",
    "ml",
    "pcs",
    "pack",
    "box",
    "bottle"
]

export default function AddProduct() {
    const navigate = useNavigate()
    const [isLoading, setIsLoading] = useState(false)
    const [formData, setFormData] = useState<ProductFormData>({
        name: "",
        category: "",
        description: "",
        price: "",
        costPrice: "",
        stock: "",
        minStock: "",
        unit: "",
        barcode: "",
        supplier: ""
    })

    const handleInputChange = (field: keyof ProductFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)

        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000))

            // Here you would typically make an API call to save the product
            console.log("Product data:", formData)

            // Navigate back to inventory after successful save
            navigate("/inventory")
        } catch (error) {
            console.error("Error saving product:", error)
        } finally {
            setIsLoading(false)
        }
    }

    const handleCancel = () => {
        navigate("/inventory")
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancel}
                    className="flex items-center gap-2"
                >
                    <ArrowLeft className="h-4 w-4" />
                    Back to Inventory
                </Button>
                <div>
                    <h1 className="text-2xl font-bold">Add New Product</h1>
                    <p className="text-muted-foreground">
                        Add a new product to your inventory
                    </p>
                </div>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit}>
                <Card>
                    <CardHeader>
                        <CardTitle>Product Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Basic Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Product Name *</Label>
                                <Input
                                    id="name"
                                    placeholder="Enter product name"
                                    value={formData.name}
                                    onChange={(e) => handleInputChange("name", e.target.value)}
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="category">Category *</Label>
                                <Select
                                    value={formData.category}
                                    onValueChange={(value) => handleInputChange("category", value)}
                                    required
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {categories.map((category) => (
                                            <SelectItem key={category} value={category}>
                                                {category}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                placeholder="Enter product description"
                                value={formData.description}
                                onChange={(e) => handleInputChange("description", e.target.value)}
                                rows={3}
                            />
                        </div>

                        {/* Pricing */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="price">Selling Price (₹) *</Label>
                                <Input
                                    id="price"
                                    type="number"
                                    step="0.01"
                                    placeholder="0.00"
                                    value={formData.price}
                                    onChange={(e) => handleInputChange("price", e.target.value)}
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="costPrice">Cost Price (₹)</Label>
                                <Input
                                    id="costPrice"
                                    type="number"
                                    step="0.01"
                                    placeholder="0.00"
                                    value={formData.costPrice}
                                    onChange={(e) => handleInputChange("costPrice", e.target.value)}
                                />
                            </div>
                        </div>

                        {/* Stock Information */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="stock">Current Stock *</Label>
                                <Input
                                    id="stock"
                                    type="number"
                                    placeholder="0"
                                    value={formData.stock}
                                    onChange={(e) => handleInputChange("stock", e.target.value)}
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="minStock">Minimum Stock</Label>
                                <Input
                                    id="minStock"
                                    type="number"
                                    placeholder="0"
                                    value={formData.minStock}
                                    onChange={(e) => handleInputChange("minStock", e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="unit">Unit *</Label>
                                <Select
                                    value={formData.unit}
                                    onValueChange={(value) => handleInputChange("unit", value)}
                                    required
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select unit" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {units.map((unit) => (
                                            <SelectItem key={unit} value={unit}>
                                                {unit}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        {/* Additional Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="barcode">Barcode/SKU</Label>
                                <Input
                                    id="barcode"
                                    placeholder="Enter barcode or SKU"
                                    value={formData.barcode}
                                    onChange={(e) => handleInputChange("barcode", e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="supplier">Supplier</Label>
                                <Input
                                    id="supplier"
                                    placeholder="Enter supplier name"
                                    value={formData.supplier}
                                    onChange={(e) => handleInputChange("supplier", e.target.value)}
                                />
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 pt-6">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleCancel}
                                disabled={isLoading}
                            >
                                <X className="h-4 w-4 mr-2" />
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={isLoading}
                            >
                                <Save className="h-4 w-4 mr-2" />
                                {isLoading ? "Saving..." : "Save Product"}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </form>
        </div>
    )
}